import Link from 'next/link';

export default function Hero() {
  return (
    <section className="relative bg-gradient-to-br from-primary-800 via-primary-700 to-primary-600 text-cream-50 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-primary-900"></div>
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-left">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-serif font-bold leading-tight mb-6">
              Premium Cannabis
              <span className="block text-gold-300">Crafted with Care</span>
            </h1>
            
            <p className="text-xl lg:text-2xl text-cream-200 mb-8 leading-relaxed">
              Discover Colorado's finest selection of cannabis products, 
              expertly curated for quality, potency, and purity.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link
                href="/products"
                className="inline-flex items-center justify-center px-8 py-4 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2 focus:ring-offset-primary-800"
              >
                Shop Products
                <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
              
              <Link
                href="/locations"
                className="inline-flex items-center justify-center px-8 py-4 bg-transparent text-cream-50 font-semibold rounded-lg border-2 border-cream-50 hover:bg-cream-50 hover:text-primary-800 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cream-50 focus:ring-offset-2 focus:ring-offset-primary-800"
              >
                Find Locations
                <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </Link>
            </div>
          </div>
          
          {/* Image/Visual Element */}
          <div className="relative">
            <div className="aspect-square bg-gradient-to-br from-gold-400 to-gold-600 rounded-2xl shadow-medium p-8 flex items-center justify-center">
              {/* Placeholder for product image or cannabis leaf illustration */}
              <div className="text-center">
                <div className="w-32 h-32 mx-auto mb-4 bg-primary-800 rounded-full flex items-center justify-center">
                  <svg className="w-16 h-16 text-gold-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </div>
                <h3 className="text-2xl font-serif font-bold text-primary-800 mb-2">
                  Quality Assured
                </h3>
                <p className="text-primary-700">
                  Lab-tested for purity and potency
                </p>
              </div>
            </div>
            
            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-sage-300 rounded-full opacity-80"></div>
            <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-gold-300 rounded-full opacity-60"></div>
          </div>
        </div>
      </div>
      
      {/* Bottom wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0Z" fill="#f8f6f0"/>
        </svg>
      </div>
    </section>
  );
}
