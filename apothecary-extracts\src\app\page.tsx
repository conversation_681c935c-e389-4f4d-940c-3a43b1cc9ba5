'use client';

import { useState } from 'react';
import Navigation from '@/components/Navigation';
import AgeVerification from '@/components/AgeVerification';

export default function Home() {
  const [isAgeVerified, setIsAgeVerified] = useState(false);

  return (
    <>
      {!isAgeVerified && (
        <AgeVerification onVerified={() => setIsAgeVerified(true)} />
      )}

      {isAgeVerified && (
        <div className="min-h-screen bg-cream-100">
          <Navigation />

          <main>
            {/* Hero Section */}
            <section className="relative bg-gradient-to-br from-primary-800 via-primary-700 to-primary-600 text-cream-50 py-24">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center">
                  <h1 className="text-4xl sm:text-5xl lg:text-6xl font-serif font-bold leading-tight mb-6">
                    Premium Cannabis
                    <span className="block text-gold-300">Crafted with Care</span>
                  </h1>

                  <p className="text-xl lg:text-2xl text-cream-200 mb-8 leading-relaxed max-w-3xl mx-auto">
                    Discover Colorado&apos;s finest selection of cannabis products,
                    expertly curated for quality, potency, and purity.
                  </p>

                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <a
                      href="/products"
                      className="inline-flex items-center justify-center px-8 py-4 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200"
                    >
                      Shop Products
                    </a>

                    <a
                      href="/locations"
                      className="inline-flex items-center justify-center px-8 py-4 bg-transparent text-cream-50 font-semibold rounded-lg border-2 border-cream-50 hover:bg-cream-50 hover:text-primary-800 transition-colors duration-200"
                    >
                      Find Locations
                    </a>
                  </div>
                </div>
              </div>
            </section>

            {/* Product Categories Section */}
            <section className="py-20 bg-cream-100">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-16">
                  <h2 className="text-4xl font-serif font-bold text-primary-800 mb-4">
                    Our Product Categories
                  </h2>
                  <p className="text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed">
                    Explore our carefully curated selection of premium cannabis products.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  <div className="bg-cream-50 rounded-xl p-6 shadow-soft hover:shadow-medium transition-all duration-300">
                    <h3 className="text-xl font-semibold text-primary-800 mb-2">Premium Flower</h3>
                    <p className="text-charcoal-600 text-sm mb-4">Hand-selected, top-shelf cannabis flower.</p>
                    <a href="/products" className="text-primary-700 hover:text-primary-800 font-medium text-sm">
                      Explore Flower →
                    </a>
                  </div>

                  <div className="bg-cream-50 rounded-xl p-6 shadow-soft hover:shadow-medium transition-all duration-300">
                    <h3 className="text-xl font-semibold text-primary-800 mb-2">Concentrates</h3>
                    <p className="text-charcoal-600 text-sm mb-4">Pure, potent concentrates and extracts.</p>
                    <a href="/products" className="text-primary-700 hover:text-primary-800 font-medium text-sm">
                      Explore Concentrates →
                    </a>
                  </div>

                  <div className="bg-cream-50 rounded-xl p-6 shadow-soft hover:shadow-medium transition-all duration-300">
                    <h3 className="text-xl font-semibold text-primary-800 mb-2">Edibles</h3>
                    <p className="text-charcoal-600 text-sm mb-4">Delicious, precisely dosed edibles.</p>
                    <a href="/products" className="text-primary-700 hover:text-primary-800 font-medium text-sm">
                      Explore Edibles →
                    </a>
                  </div>

                  <div className="bg-cream-50 rounded-xl p-6 shadow-soft hover:shadow-medium transition-all duration-300">
                    <h3 className="text-xl font-semibold text-primary-800 mb-2">Topicals</h3>
                    <p className="text-charcoal-600 text-sm mb-4">Therapeutic cannabis topicals.</p>
                    <a href="/products" className="text-primary-700 hover:text-primary-800 font-medium text-sm">
                      Explore Topicals →
                    </a>
                  </div>
                </div>
              </div>
            </section>

            {/* Newsletter Section */}
            <section className="py-20 bg-primary-800">
              <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 className="text-3xl font-serif font-bold text-cream-50 mb-4">
                  Stay Updated with Apothecary Extracts
                </h2>
                <p className="text-xl text-cream-200 mb-8">
                  Get the latest news on new products, special offers, and cannabis education.
                </p>
                <form className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="flex-1 px-4 py-3 rounded-lg border border-cream-300 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:border-transparent text-charcoal-800"
                    required
                  />
                  <button
                    type="submit"
                    className="px-8 py-3 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2 focus:ring-offset-primary-800"
                  >
                    Subscribe
                  </button>
                </form>
                <p className="text-sm text-cream-300 mt-4">
                  We respect your privacy. Unsubscribe at any time.
                </p>
              </div>
            </section>
          </main>

          {/* Footer */}
          <footer className="bg-charcoal-800 text-cream-100 py-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                {/* Company Info */}
                <div className="col-span-1 md:col-span-2">
                  <div className="text-2xl font-serif font-bold text-cream-50 mb-4">
                    Apothecary Extracts
                  </div>
                  <p className="text-cream-300 mb-4 leading-relaxed">
                    Colorado&apos;s premier cannabis dispensary, committed to providing
                    the highest quality products and exceptional customer service.
                  </p>
                  <div className="flex space-x-4">
                    <a href="#" className="text-cream-300 hover:text-gold-400 transition-colors">
                      <span className="sr-only">Facebook</span>
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                      </svg>
                    </a>
                    <a href="#" className="text-cream-300 hover:text-gold-400 transition-colors">
                      <span className="sr-only">Instagram</span>
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"/>
                      </svg>
                    </a>
                  </div>
                </div>

                {/* Quick Links */}
                <div>
                  <h3 className="text-lg font-semibold text-cream-50 mb-4">Quick Links</h3>
                  <ul className="space-y-2">
                    <li><a href="/products" className="text-cream-300 hover:text-gold-400 transition-colors">Products</a></li>
                    <li><a href="/locations" className="text-cream-300 hover:text-gold-400 transition-colors">Locations</a></li>
                    <li><a href="/about" className="text-cream-300 hover:text-gold-400 transition-colors">About Us</a></li>
                    <li><a href="/education" className="text-cream-300 hover:text-gold-400 transition-colors">Education</a></li>
                  </ul>
                </div>

                {/* Legal */}
                <div>
                  <h3 className="text-lg font-semibold text-cream-50 mb-4">Legal</h3>
                  <ul className="space-y-2">
                    <li><a href="/privacy" className="text-cream-300 hover:text-gold-400 transition-colors">Privacy Policy</a></li>
                    <li><a href="/terms" className="text-cream-300 hover:text-gold-400 transition-colors">Terms of Service</a></li>
                    <li><a href="/compliance" className="text-cream-300 hover:text-gold-400 transition-colors">Compliance</a></li>
                  </ul>
                </div>
              </div>

              <div className="border-t border-charcoal-600 mt-12 pt-8 text-center">
                <p className="text-cream-400 text-sm">
                  © 2025 Apothecary Extracts. All rights reserved. |
                  Licensed Cannabis Retailer | Must be 21+ to purchase
                </p>
              </div>
            </div>
          </footer>
        </div>
      )}
    </>
  );
}



