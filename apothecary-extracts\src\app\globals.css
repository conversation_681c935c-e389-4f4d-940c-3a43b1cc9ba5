@import "tailwindcss";

:root {
  --background: #f8f6f0;
  --foreground: #2d3436;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  
  --color-primary-50: #f0f9f4;
  --color-primary-100: #dcf2e4;
  --color-primary-200: #bce5cd;
  --color-primary-300: #8dd1a8;
  --color-primary-400: #57b67c;
  --color-primary-500: #359a5a;
  --color-primary-600: #267d47;
  --color-primary-700: #1f633a;
  --color-primary-800: #1b4332;
  --color-primary-900: #163a2b;
  
  --color-cream-50: #fefefe;
  --color-cream-100: #f8f6f0;
  --color-cream-200: #f4f1e8;
  --color-cream-300: #ede8db;
  --color-cream-400: #e4dcc8;
  --color-cream-500: #d8cdb0;
  
  --color-gold-50: #fefcf7;
  --color-gold-100: #fdf8ed;
  --color-gold-200: #f9eed5;
  --color-gold-300: #f4e4bc;
  --color-gold-400: #edd5a3;
  --color-gold-500: #d4a574;
  
  --color-charcoal-50: #f8f9fa;
  --color-charcoal-100: #e9ecef;
  --color-charcoal-200: #dee2e6;
  --color-charcoal-300: #ced4da;
  --color-charcoal-400: #adb5bd;
  --color-charcoal-500: #6c757d;
  --color-charcoal-600: #495057;
  --color-charcoal-700: #343a40;
  --color-charcoal-800: #2d3436;
  --color-charcoal-900: #212529;
  
  --color-sage-50: #f7f9f8;
  --color-sage-100: #eef2f0;
  --color-sage-200: #dde5e1;
  --color-sage-300: #c4d2ca;
  --color-sage-400: #a5b8ad;
  --color-sage-500: #95a99c;
  
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-serif: 'Playfair Display', Georgia, serif;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
}

html {
  scroll-behavior: smooth;
}

*:focus {
  outline: 2px solid var(--color-primary-600);
  outline-offset: 2px;
}
